import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, Modal, ActivityIndicator, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { importExcel, exportToLocal } from '../utils/excel';
import * as Localization from 'expo-localization';
import * as StoreReview from 'expo-store-review';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { useTransactionContext } from '../context/TransactionContext';
import * as RevenueCat from '../utils/revenueCat';
import { ErrorBoundary, safeAsyncOperation } from '../components/ErrorBoundary';

interface MenuItem {
    id: string;
    title: string;
    icon: string;
    color: string;
    route?: string;
    onPress?: () => void;
    isPremium?: boolean;
}

const PREMIUM_STATUS_KEY = 'premium_status';

const Profile = () => {
    const [showExportModal, setShowExportModal] = useState(false);
    const [showImportModal, setShowImportModal] = useState(false);

    const [isPremium, setIsPremium] = useState(false);
    const [showPremiumModal, setShowPremiumModal] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const { triggerRefresh } = useTransactionContext();
    const [isLoadingPurchase, setIsLoadingPurchase] = useState(false);
    const [currentOffering, setCurrentOffering] = useState<any>(null);

    const [showRedeemModal, setShowRedeemModal] = useState(false);
    const [redeemCode, setRedeemCode] = useState('');
    const [isRedeeming, setIsRedeeming] = useState(false);

    // 检查是否在促销期内 (7/4-7/31)
    const isPromotionActive = () => {
        // 使用新的 API 获取地区和语言信息
        const locales = Localization.getLocales();
        const primaryLocale = locales[0];
        const region = primaryLocale?.regionCode; // e.g. "CN"
        const languageTag = primaryLocale?.languageTag; // e.g. "zh-CN"

        const isValid = region?.toUpperCase() === "CN" ||
                       languageTag?.toLowerCase().includes("cn") ||
                       languageTag?.toLowerCase().includes("zh");

        const now = new Date();
        const startDate = new Date('2025-07-04');
        const endDate = new Date('2025-07-31');
        endDate.setHours(23, 59, 59, 999); // 设置为当天结束
        return isValid && now >= startDate && now <= endDate;
    };

    useEffect(() => {
        // 检查用户是否已购买高级版
        checkPremiumStatus();
        const initPurchases = async (retryCount = 0) => {
            try {
                // 初始化 RevenueCat
                const initialized = await RevenueCat.initializeRevenueCat();
                if (!initialized) {
                    console.log('RevenueCat not available on this platform');
                    return;
                }

                // 获取产品信息，带重试机制
                let offering = await RevenueCat.getOfferings();

                // 如果第一次获取失败，等待2秒后重试
                if (!offering && retryCount < 2) {
                    console.log(`Retrying to get offerings... (attempt ${retryCount + 1})`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    offering = await RevenueCat.getOfferings();
                }

                setCurrentOffering(offering);

                // 检查购买历史，恢复之前的购买
                await restorePurchases();
            } catch (error) {
                console.error('Failed to initialize purchases:', error);

                // 如果初始化失败且重试次数少于2次，则重试
                if (retryCount < 2) {
                    console.log(`Retrying initialization... (attempt ${retryCount + 1})`);
                    setTimeout(() => initPurchases(retryCount + 1), 3000);
                }
            }
        };

        initPurchases();

        // 组件卸载时的清理工作
        return () => {
            // RevenueCat 不需要手动断开连接
            console.log('Profile component unmounted');
        };
    }, []);

    const restorePurchases = async () => {
        try {
            // const restored = await RevenueCat.restorePurchases();
            const restored = true; // todo restored

            if (restored) {
                setIsPremium(true);
                console.log('Premium purchase restored');

                setShowPremiumModal(false);

                // 刷新界面以显示解锁的功能
                triggerRefresh();
            } else {
                setIsPremium(false);
                console.log('No premium purchase found');
            }
        } catch (error) {
            console.error('Failed to restore purchases:', error);
        }
    };

    const checkPremiumStatus = async () => {
        try {
            // 使用 RevenueCat 检查购买状态
            const isPremium = await RevenueCat.checkPremiumStatus();
            setIsPremium(isPremium);
        } catch (error) {
            console.error('Failed to check premium status:', error);
        }
    };

    const handleSuccessfulPurchase = async () => {
        try {
            // 更新购买状态
            setIsPremium(true);
            await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');

            // 通知用户
            Alert.alert(
                i18n.t('profile.premium.success'),
                i18n.t('profile.premium.enjoyFeatures')
            );

            setShowPremiumModal(false);

            // 刷新界面以显示解锁的功能
            triggerRefresh();
        } catch (error) {
            console.error('Failed to handle purchase:', error);
        }
    };

    const handlePurchasePremium = async () => {
        await safeAsyncOperation(
            async () => {
                setIsLoadingPurchase(true);

                try {
                    // 如果没有产品信息，尝试重新获取
                    let offering = currentOffering;
                    if (!offering || !offering.availablePackages || offering.availablePackages.length === 0) {
                        console.log('No offering available, attempting to refresh...');
                        offering = await RevenueCat.getOfferings();
                        setCurrentOffering(offering);
                    }

                    // 再次检查是否有可用的产品
                    if (!offering || !offering.availablePackages || offering.availablePackages.length === 0) {
                        Alert.alert(
                            i18n.t('profile.premium.failed'),
                            'Product not available. Please check your network connection and try again.'
                        );
                        setIsLoadingPurchase(false);
                        return;
                    }

                    // 获取第一个可用的包（通常是高级版）
                    const packageToPurchase = offering.availablePackages[0];

                    // 发起购买
                    const success = await RevenueCat.purchasePackage(packageToPurchase);

                    if (success) {
                        await handleSuccessfulPurchase();
                    } else {
                        Alert.alert(
                            i18n.t('profile.premium.failed'),
                            i18n.t('profile.premium.tryAgain')
                        );
                    }
                } catch (error) {
                    console.error('Purchase error:', error);
                    Alert.alert(
                        i18n.t('profile.premium.failed'),
                        'An error occurred during purchase. Please try again.'
                    );
                }

                setIsLoadingPurchase(false);
            },
            undefined,
            'Purchase premium failed'
        ).catch(() => {
            // 确保在任何错误情况下都重置加载状态
            Alert.alert(
                i18n.t('profile.premium.failed'),
                i18n.t('profile.premium.tryAgain')
            );
            setIsLoadingPurchase(false);
        });
    };



    const handleExportToLocal = async () => {
        try {
            await exportToLocal();
            Alert.alert(i18n.t('profile.export.success'), i18n.t('profile.export.localExportSuccess'));
            setShowExportModal(false);
        } catch (error) {
            Alert.alert(i18n.t('common.error'), i18n.t('profile.export.failed'));
        }
    };

    const handleImport = async () => {
        try {
            setImportLoading(true);

            // 选择文件
            const result = await DocumentPicker.getDocumentAsync({
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                copyToCacheDirectory: true
            });

            if (result.canceled) {
                setImportLoading(false);
                return;
            }

            const fileUri = result.assets[0].uri;

            // 检查文件是否存在
            const fileInfo = await FileSystem.getInfoAsync(fileUri);
            if (!fileInfo.exists) {
                Alert.alert(i18n.t('common.error'), i18n.t('profile.import.fileNotFound'));
                setImportLoading(false);
                return;
            }

            // 导入Excel，传递高级版状态
            const importCount = await importExcel(fileUri, isPremium);

            // 刷新交易列表
            triggerRefresh();

            // 显示成功消息
            Alert.alert(
                i18n.t('profile.import.success'),
                i18n.t('profile.import.successMessage').replace('{count}', importCount.toString())
            );

            setShowImportModal(false);
        } catch (error) {
            console.error('Import failed:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('profile.import.failed'));
        } finally {
            setImportLoading(false);
        }
    };

    const handleTagsPress = () => {
        if (isPremium) {
            router.push('/screens/tags');
        } else {
            setShowPremiumModal(true);
        }
    };

    const handlePrepaidCardPress = () => {
        if (isPremium) {
            router.push('/screens/prepaidCards');
        } else {
            setShowPremiumModal(true);
        }
    };

    const handlePlannedPurchasePress = () => {
        if (isPremium) {
            router.push('/screens/plannedPurchases');
        } else {
            setShowPremiumModal(true);
        }
    };

    const handleLendingPress = () => {
        if (isPremium) {
            router.push('/screens/personalLending');
        } else {
            setShowPremiumModal(true);
        }
    };

    const handleReimbursementPress = () => {
        router.push('/screens/reimbursement');
    };
    

    const handleShoppingPress  = () => {
        if (isPremium) {
            router.push('/screens/shoppingPlatforms');
        } else {
            setShowPremiumModal(true);
        }
    };

    const handleRateApp = async () => {
        try {
            console.log('Rate app button pressed');

            // 检查是否支持应用内评价
            const hasAction = await StoreReview.hasAction();
            console.log('StoreReview hasAction:', hasAction);

            if (hasAction) {
                // 使用应用内评价
                await StoreReview.requestReview();
                console.log('StoreReview requested');

                // 显示感谢消息
                Alert.alert(
                    i18n.t('common.thanks'),
                    i18n.t('profile.rateAppThanks') || '感谢您的评价！'
                );
            } else {
                // 备用方案：显示提示让用户手动去应用商店评价
                Alert.alert(
                    i18n.t('profile.rateApp') || '评价应用',
                    i18n.t('profile.rateAppMessage') || '请前往应用商店为我们评价，您的反馈对我们很重要！',
                    [
                        { text: i18n.t('common.cancel') || '取消', style: 'cancel' },
                        {
                            text: i18n.t('common.ok') || '好的',
                            onPress: () => {
                                console.log('User confirmed to rate app manually');
                                // 显示感谢消息
                                Alert.alert(
                                    i18n.t('common.thanks') || '谢谢',
                                    '感谢您的支持！'
                                );
                            }
                        }
                    ]
                );
            }
        } catch (error) {
            console.error('Error in handleRateApp:', error);
            // 如果出错，显示一个简单的感谢消息
            Alert.alert(
                i18n.t('common.thanks') || '谢谢',
                i18n.t('profile.rateAppThanks') || '感谢您使用我们的应用！'
            );
        }
    };

    const renderExportModal = () => (
        <Modal
            visible={showExportModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowExportModal(false)}
        >
            <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => setShowExportModal(false)}
            >
                <View style={styles.exportModal}>
                    <Text style={styles.exportTitle}>{i18n.t('profile.export.title')}</Text>

                    <View style={styles.exportOptions}>
                        <TouchableOpacity
                            style={styles.exportOptionButton}
                            onPress={handleExportToLocal}
                        >
                            <Ionicons name="download-outline" size={24} color="#dc4446" />
                            <Text style={styles.exportOptionText}>{i18n.t('profile.export.exportToLocal')}</Text>
                        </TouchableOpacity>

                        {/* todo: email export */}
                        {/* <View style={styles.exportDivider} /> */}
                        {/* {renderExportByEmail()} */}
                    </View>
                </View>
            </TouchableOpacity>
        </Modal>
    );



    const renderImportModal = () => (
        <Modal
            visible={showImportModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => !importLoading && setShowImportModal(false)}
        >
            <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => !importLoading && setShowImportModal(false)}
            >
                <View style={styles.importModal} onStartShouldSetResponder={() => true}>
                    <Text style={styles.importTitle}>{i18n.t('profile.import.title')}</Text>

                    <Text style={styles.importInstructions}>
                        {i18n.t('profile.import.instructions')}
                    </Text>

                    {!isPremium && (
                        <Text style={styles.importWarning}>
                            {i18n.t('profile.import.instructionsPremium')}
                        </Text>
                    )}

                    {importLoading ? (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator size="large" color="#dc4446" />
                            <Text style={styles.loadingText}>{i18n.t('profile.import.processing')}</Text>
                        </View>
                    ) : (
                        <View style={styles.importButtons}>
                            <TouchableOpacity
                                style={[styles.importButton, styles.cancelButton]}
                                onPress={() => setShowImportModal(false)}
                            >
                                <Text style={styles.importButtonText}>{i18n.t('common.cancel')}</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.importButton, styles.confirmButton]}
                                onPress={handleImport}
                            >
                                <Text style={styles.importButtonText}>{i18n.t('profile.import.selectFile')}</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        </Modal>
    );

    const renderPremiumModal = () => (
        <Modal
            visible={showPremiumModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => !isLoadingPurchase && setShowPremiumModal(false)}
        >
            <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => !isLoadingPurchase && setShowPremiumModal(false)}
            >
                <View style={styles.premiumModal} onStartShouldSetResponder={() => true}>
                    <Text style={styles.premiumTitle}>{i18n.t('profile.premium.title')}</Text>

                    {/* 促销横幅 */}
                    {isPromotionActive() && (
                        <View style={styles.promotionBanner}>
                            <View style={styles.promotionHeader}>
                                <Text style={styles.promotionBadge}>🎉 限时优惠</Text>
                                <Text style={styles.promotionEndDate}>7月31日截止</Text>
                            </View>
                            <View style={styles.priceContainer}>
                                <Text style={styles.originalPrice}>¥38</Text>
                                <Text style={styles.discountPrice}>¥15</Text>
                                <View style={styles.discountBadge}>
                                    <Text style={styles.discountText}>4折</Text>
                                </View>
                            </View>
                            <Text style={styles.promotionFeatures}>✨ 购买一次，终身有效</Text>
                            <Text style={styles.promotionFeatures}>🚀 后续新功能免费更新</Text>
                        </View>
                    )}

                    <Text style={styles.premiumDescription}>{i18n.t('profile.premium.description')}</Text>

                    <View style={styles.premiumFeatures}>
                        <View style={styles.premiumFeatureItem}>
                            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
                            <Text style={styles.premiumFeatureText}>{i18n.t('profile.premium.feature1')}</Text>
                        </View>
                        <View style={styles.premiumFeatureItem}>
                            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
                            <Text style={styles.premiumFeatureText}>{i18n.t('profile.premium.feature2')}</Text>
                        </View>
                         <View style={styles.premiumFeatureItem}>
                            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
                            <Text style={styles.premiumFeatureText}>{i18n.t('profile.premium.feature3')}</Text>
                        </View>
                        <View style={styles.premiumFeatureItem}>
                            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
                            <Text style={styles.premiumFeatureText}>{i18n.t('profile.premium.feature4')}</Text>
                        </View>
                    </View>

                    <TouchableOpacity
                        style={styles.purchaseButton}
                        onPress={handlePurchasePremium}
                        disabled={isLoadingPurchase}
                    >
                        {isLoadingPurchase ? (
                            <ActivityIndicator color="white" />
                        ) : (
                            <Text style={styles.purchaseButtonText}>{i18n.t('profile.premium.purchase')}</Text>
                        )}
                    </TouchableOpacity>

                    {!isLoadingPurchase && (
                        <>
                            <TouchableOpacity
                                style={styles.restoreButton}
                                onPress={restorePurchases}
                            >
                                <Text style={styles.restoreButtonText}>{i18n.t('profile.premium.restore')}</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.redeemCodeButton}
                                onPress={() => setShowRedeemModal(true)}
                            >
                                <Text style={styles.redeemCodeButtonText}>{i18n.t('profile.premium.redeemCode')}</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => setShowPremiumModal(false)}
                            >
                                <Text style={styles.closeButtonText}>{i18n.t('common.cancel')}</Text>
                            </TouchableOpacity>
                        </>
                    )}
                </View>
            </TouchableOpacity>
        </Modal>
    );

    const handleRedeemCode = async () => {
        // 检查兑换码是否为空
        if (!redeemCode.trim()) {
            Alert.alert('错误', '请输入兑换码');
            return;
        }

        await safeAsyncOperation(
            async () => {
                setIsRedeeming(true);

                try {
                    // 直接打开Apple的兑换页面，不需要预先输入代码
                    const success = await RevenueCat.presentCodeRedemptionSheet();

                    if (success) {
                        setIsPremium(true);
                        Alert.alert(
                            i18n.t('profile.premium.redeemSuccess'),
                            i18n.t('profile.premium.redeemSuccessMessage')
                        );
                        setShowRedeemModal(false);
                        setRedeemCode('');
                        triggerRefresh();
                    }
                } catch (error) {
                    console.error('Failed to present Apple redemption sheet:', error);
                    Alert.alert(
                        i18n.t('profile.premium.redeemFailed'),
                        i18n.t('profile.premium.redeemError')
                    );
                }

                setIsRedeeming(false);
            },
            '打开Apple兑换页面时出错'
        );
    };

    const renderRedeemModal = () => (
        <Modal
            visible={showRedeemModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => !isRedeeming && setShowRedeemModal(false)}
        >
            <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => !isRedeeming && setShowRedeemModal(false)}
            >
                <View style={styles.redeemModal} onStartShouldSetResponder={() => true}>
                    <Text style={styles.redeemTitle}>{i18n.t('profile.premium.redeemTitle')}</Text>
                    <Text style={styles.redeemDescription}>{i18n.t('profile.premium.redeemDescriptionNew')}</Text>

                        <TouchableOpacity
                            style={[styles.redeemButton, isRedeeming && styles.redeemButtonDisabled]}
                            onPress={handleRedeemCode}
                            disabled={isRedeeming}
                        >
                            {isRedeeming ? (
                                <ActivityIndicator color="white" />
                            ) : (
                            <Text style={styles.redeemButtonText}>{i18n.t('profile.premium.openRedemption')}</Text>
                            )}
                        </TouchableOpacity>

                    {!isRedeeming && (
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => setShowRedeemModal(false)}
                        >
                            <Text style={styles.closeButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </TouchableOpacity>
        </Modal>
    );

    const menuItems: MenuItem[] = [
        {
            id: 'budget',
            title: i18n.t('profile.memberBudget'),
            icon: 'people-outline',
            color: '#4CAF50',
            route: '/screens/budget',
        },
        {
            id: 'categories',
            title: i18n.t('profile.manageCategories'),
            icon: 'list-outline',
            color: '#FF9800',
            route: '/screens/categories',
        },
        {
            id: 'tags',
            title: i18n.t('profile.manageTags'),
            icon: 'pricetags-outline',
            color: '#9C27B0',
            onPress: handleTagsPress,
            isPremium: true,
        },
        {
            id: 'prepaidCards',
            title: i18n.t('prepaidCards.title'),
            icon: 'card-outline',
            color: '#E91E63',
            onPress: handlePrepaidCardPress,
            isPremium: true,
        },
        {
            id: 'shoppingPlatforms',
            title: i18n.t('settings.shoppingPlatforms'),
            icon: 'storefront-outline',
            color: '#FF5722',
            onPress: handleShoppingPress,
            isPremium: true,
        },
        {
            id: 'plannedPurchases',
            title: i18n.t('plannedPurchases.title'),
            icon: 'list-circle-outline',
            color: '#673AB7',
            onPress: handlePlannedPurchasePress,
            isPremium: true,
        },
        {
            id: 'personalLending',
            title: i18n.t('lending.title'),
            icon: 'people-outline',
            color: '#795548',
            onPress: handleLendingPress,
            isPremium: true,
        },
        // {
        //     id: 'reimbursement',
        //     title: i18n.t('reimbursement.title'),
        //     icon: 'receipt-outline',
        //     color: '#607D8B',
        //     onPress: handleReimbursementPress,
        // },
        {
            id: 'export',
            title: i18n.t('profile.exportExcel'),
            icon: 'document-outline',
            color: '#2196F3',
            onPress: () => setShowExportModal(true),
        },
        {
            id: 'import',
            title: i18n.t('profile.importExcel'),
            icon: 'cloud-upload-outline',
            color: '#FF5722',
            onPress: () => setShowImportModal(true),
        },
        {
            id: 'settings',
            title: i18n.t('profile.settings'),
            icon: 'settings-outline',
            color: '#607D8B',
            route: '/screens/settings',
        },
        {
            id: 'rate',
            title: i18n.t('profile.rateApp'),
            icon: 'star-outline',
            color: '#FFC107',
            onPress: handleRateApp,
        },
    ];

    return (
        <ErrorBoundary
            onError={(error, errorInfo) => {
                console.error('🚨 Profile screen error:', error);
                console.error('Error info:', errorInfo);
            }}
        >
            <View style={styles.container}>
                <ScrollView
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                >
                    {/* <View style={styles.header}>
                        <View style={styles.avatarContainer}>
                            <Image
                                source={require('../../assets/images/logof.png')}
                                style={styles.avatarImage}
                                resizeMode="contain"
                            />
                        </View>
                        <Text style={styles.appName}>{i18n.t('profile.appName')}</Text>
                    </View> */}
                    {/* 数据安全提醒 - 可折叠的文字提示
                    <View style={styles.dataWarningContainer}>
                        <TouchableOpacity
                            style={styles.dataWarningTextButton}
                            onPress={() => setShowDataWarning(!showDataWarning)}
                        >
                            <Text style={styles.dataWarningText}>
                                {i18n.t('profile.dataWarning.shortText')}
                            </Text>
                            <Ionicons
                                name={showDataWarning ? "chevron-up" : "chevron-down"}
                                size={16}
                                color="#FF9800"
                            />
                        </TouchableOpacity>

                        {showDataWarning && (
                            <View style={styles.warningCardOverlay}>
                                <View style={styles.warningCard}>
                                    <View style={styles.warningHeader}>
                                        <Ionicons name="shield-checkmark" size={20} color="#FF9800" />
                                        <Text style={styles.warningTitle}>{i18n.t('profile.dataWarning.title')}</Text>
                                        <TouchableOpacity
                                            style={styles.closeWarningButton}
                                            onPress={() => setShowDataWarning(false)}
                                        >
                                            <Ionicons name="close" size={20} color="#666" />
                                        </TouchableOpacity>
                                    </View>
                                    <Text style={styles.warningMessage}>{i18n.t('profile.dataWarning.message')}</Text>
                                    <Text style={styles.warningTip}>{i18n.t('profile.dataWarning.exportReminder')}</Text>
                                </View>
                            </View>
                        )}
                    </View> */}
                    <View style={styles.menuSection}>
                        {menuItems.map((item, index) => (
                            <TouchableOpacity
                                key={item.id}
                                style={[
                                    styles.menuItem,
                                    index === menuItems.length - 1 && styles.lastMenuItem
                                ]}
                                onPress={item.onPress || (item.route ? () => router.push(item.route as any) : undefined)}
                                activeOpacity={0.7}
                            >
                                <View style={[styles.menuIcon, { backgroundColor: `${item.color}20` }]}>
                                    <Ionicons name={item.icon as any} size={24} color={item.color} />
                                </View>
                                <View style={styles.menuContent}>
                                    <View style={styles.menuTitleContainer}>
                                        <Text style={styles.menuTitle}>{item.title}</Text>
                                        {item.isPremium && !isPremium && (
                                            <View style={styles.premiumBadge}>
                                                <Text style={styles.premiumBadgeText}>{i18n.t('profile.premium.badge')}</Text>
                                            </View>
                                        )}
                                    </View>
                                    <Ionicons name="chevron-forward" size={20} color="#ccc" />
                                </View>
                            </TouchableOpacity>
                        ))}
                    </View>

                </ScrollView>

                {renderExportModal()}
                {renderImportModal()}
                {renderPremiumModal()}
                {renderRedeemModal()}

            </View>
        </ErrorBoundary>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollContent: {
        paddingBottom: 100, // 确保最后一个项目有足够的空间
    },
    header: {
        backgroundColor: 'white',
        padding: 20,
        alignItems: 'center',
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        marginBottom: 20,
    },
    avatarContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: '#FFF1F1',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
        overflow: 'hidden',
    },
    avatarImage: {
        width: '100%',
        height: '100%',
    },
    appName: {
        fontSize: 24,
        fontWeight: '600',
        marginBottom: 4,
    },
    version: {
        color: '#666',
        fontSize: 14,
    },
    warningCard: {
        backgroundColor: '#FFF8E1',
        borderRadius: 12,
        padding: 16,
        borderLeftWidth: 4,
        borderLeftColor: '#FF9800',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 6,
        elevation: 8,
    },
    warningHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        justifyContent: 'space-between',
    },
    warningTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#E65100',
        marginLeft: 8,
        flex: 1,
    },
    warningMessage: {
        fontSize: 14,
        color: '#BF360C',
        lineHeight: 20,
        marginBottom: 8,
    },
    warningTip: {
        fontSize: 13,
        color: '#F57C00',
        fontStyle: 'italic',
    },
    dataWarningContainer: {
        alignItems: 'flex-end',
        // marginHorizontal: 20,
        marginBottom: 20,
        position: 'relative',
        zIndex: 1000,
    },
    dataWarningIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#FFF8E1',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    dataWarningTextButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF8E1',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 3,
    },
    dataWarningText: {
        fontSize: 12,
        color: '#E65100',
        flex: 1,
        marginRight: 8,
        lineHeight: 16,
    },
    warningCardOverlay: {
        position: 'absolute',
        top: 48,
        right: 0,
        left: -20,
        zIndex: 1001,
    },
    closeWarningButton: {
        padding: 4,
    },
    menuSection: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 12,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 12,
        minHeight: 60, // 确保足够的触摸区域
    },
    lastMenuItem: {
        marginBottom: 20, // 给最后一个项目额外的底部空间
    },
    menuIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    menuContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    menuTitleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    menuTitle: {
        fontSize: 16,
        color: '#333',
    },
    premiumBadge: {
        backgroundColor: '#FFD700',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 12,
        marginRight: 8,
    },
    premiumBadgeText: {
        color: '#333',
        fontSize: 12,
        fontWeight: '600',
    },
    exportModal: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        width: '80%',
        alignSelf: 'center',
    },
    exportTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
        textAlign: 'center',
    },
    exportOptions: {
        width: '100%',
    },
    exportOptionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        marginBottom: 16,
    },
    exportOptionText: {
        fontSize: 16,
        color: '#333',
        marginLeft: 12,
    },
    exportDivider: {
        height: 1,
        backgroundColor: '#eee',
        marginVertical: 16,
    },
    emailExportContainer: {
        width: '100%',
    },
    emailExportTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 12,
    },
    emailInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
        fontSize: 16,
    },
    exportButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        gap: 12,
    },
    exportButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 8,
    },
    importModal: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        width: '80%',
        alignSelf: 'center',
    },
    importTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
        textAlign: 'center',
    },
    importInstructions: {
        fontSize: 14,
        color: '#666',
        marginBottom: 20,
        textAlign: 'center',
    },
    importWarning: {
        fontSize: 14,
        color: '#FF9A2E',
        marginBottom: 20,
        textAlign: 'center',
        fontWeight: '500',
        backgroundColor: '#FFF8E1',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#FFE082',
    },
    importButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        gap: 12,
    },
    importButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 8,
    },
    loadingContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    loadingText: {
        marginTop: 12,
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        backgroundColor: '#dc4446',
    },
    cancelButton: {
        backgroundColor: '#666',
    },
    exportButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },
    importButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    premiumModal: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        width: '85%',
        alignSelf: 'center',
    },
    premiumTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginBottom: 12,
        textAlign: 'center',
        color: '#333',
    },
    premiumDescription: {
        fontSize: 16,
        color: '#666',
        marginBottom: 16,
        textAlign: 'center',
    },
    premiumFeatures: {
        marginBottom: 20,
    },
    premiumFeatureItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        gap: 8,
    },
    premiumFeatureText: {
        fontSize: 16,
        color: '#333',
    },
    purchaseButton: {
        backgroundColor: '#dc4446',
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginBottom: 12,
    },
    purchaseButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    closeButton: {
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    closeButtonText: {
        color: '#666',
        fontSize: 16,
    },
    restoreButton: {
        backgroundColor: 'transparent',
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 8,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: '#dc4446',
        alignItems: 'center',
    },
    restoreButtonText: {
        color: '#dc4446',
        fontSize: 16,
        fontWeight: '500',
    },
    // 促销相关样式
    promotionBanner: {
        backgroundColor: 'linear-gradient(135deg, #FF6B6B, #FF8E53)',
        borderRadius: 16,
        padding: 16,
        marginBottom: 20,
        borderWidth: 2,
        borderColor: '#FF6B6B',
        shadowColor: '#FF6B6B',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    promotionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    promotionBadge: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#FF6B6B',
    },
    promotionEndDate: {
        fontSize: 12,
        color: '#FF8E53',
        fontWeight: '600',
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
        gap: 12,
    },
    originalPrice: {
        fontSize: 18,
        color: '#999',
        textDecorationLine: 'line-through',
    },
    discountPrice: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#FF6B6B',
    },
    discountBadge: {
        backgroundColor: '#FF6B6B',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    discountText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },
    promotionFeatures: {
        fontSize: 14,
        color: '#333',
        textAlign: 'center',
        marginBottom: 4,
    },
    // 兑换码相关样式
    redeemModal: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 24,
        width: '85%',
        maxWidth: 400,
    },
    redeemTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginBottom: 8,
        textAlign: 'center',
        color: '#333',
    },
    redeemDescription: {
        fontSize: 14,
        color: '#666',
        marginBottom: 20,
        textAlign: 'center',
        lineHeight: 20,
    },
    redeemInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
        marginBottom: 20,
        textAlign: 'center',
        backgroundColor: '#f9f9f9',
    },
    redeemButton: {
        backgroundColor: '#dc4446',
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginBottom: 12,
    },
    redeemButtonDisabled: {
        backgroundColor: '#ccc',
    },
    redeemButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    redeemCodeButton: {
        backgroundColor: 'transparent',
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 8,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: '#4CAF50',
        alignItems: 'center',
    },
    redeemCodeButtonText: {
        color: '#4CAF50',
        fontSize: 16,
        fontWeight: '500',
    },
});

export default Profile;